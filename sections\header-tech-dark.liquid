<!-- sections/tech-navbar.liquid -->
<style>
  /* CSS变量定义 */
  :root {
    --tech-primary: {{ section.settings.primary_color }};
    --tech-secondary: {{ section.settings.secondary_color }};
    --tech-bg-dark: rgba(18, 18, 35, 0.95);
    --tech-bg-darker: rgba(12, 12, 24, 0.98);
    --tech-text: #fff;
    --tech-text-muted: rgba(255, 255, 255, 0.7);
    --tech-border: rgba(255, 255, 255, 0.12);
    --tech-gradient: linear-gradient(135deg, {{ section.settings.primary_color }} 0%, {{ section.settings.secondary_color }} 100%);
    --tech-glass-blur: 15px;
    --tech-shadow-glow: 0 5px 25px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.25);
    --tech-navbar-height: 80px;
    --tech-navbar-mobile-height: 60px;
  }

  /* 导航栏容器 */
  .tech-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: var(--tech-bg-dark);
    backdrop-filter: blur(var(--tech-glass-blur));
    -webkit-backdrop-filter: blur(var(--tech-glass-blur));
    border-bottom: 1px solid var(--tech-border);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    animation: navFadeIn 0.8s ease-out;
  }

  /* 为页面内容添加顶部间距，防止被导航栏遮挡 */
  body.tech-navbar-initialized {
    padding-top: var(--tech-navbar-height);
  }

  @media only screen and (max-width: 768px) {
    body.tech-navbar-initialized {
      padding-top: var(--tech-navbar-mobile-height);
    }
  }

  .tech-navbar.scrolled {
    background: var(--tech-bg-darker);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25), 0 2px 10px var(--tech-shadow-glow);
    border-bottom-color: rgba({{ section.settings.primary_color | color_to_rgb }}, 0.2);
  }

  /* 导航栏内容 */
  .tech-navbar-inner {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--tech-navbar-height);
  }

  /* Logo区域 */
  .tech-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    z-index: 10;
  }

  .tech-logo-image {
    max-height: 60px;
    width: auto;
    display: block;
  }

  .tech-logo-icon {
    width: 45px;
    height: 45px;
    background: var(--tech-gradient);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.3);
    animation: float 3s ease-in-out infinite;
  }

  /* 主导航菜单 */
  .tech-nav-menu {
    display: flex;
    align-items: center;
    gap: 40px;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .tech-nav-item {
    position: relative;
  }

  .tech-nav-link {
    color: var(--tech-text-muted);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .tech-nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--tech-gradient);
    transition: width 0.3s ease;
  }

  .tech-nav-link:hover {
    color: var(--tech-text);
    transform: translateY(-2px);
  }

  .tech-nav-link:hover::before {
    width: 100%;
  }

  /* 有下拉菜单的一级菜单链接样式 */
  .tech-nav-link.has-dropdown {
    cursor: pointer;
    pointer-events: auto;
  }

  /* 下拉箭头动画 */
  .tech-nav-link svg {
    transition: transform 0.3s ease, color 0.3s ease;
  }

  .tech-nav-item:hover .tech-nav-link svg {
    transform: rotate(180deg);
    color: var(--tech-primary);
  }

  /* 下拉菜单 */
  .tech-dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(26, 26, 46, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid var(--tech-border);
    border-radius: 12px;
    padding: 20px;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-top: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .tech-nav-item:hover .tech-dropdown {
    opacity: 1;
    visibility: visible;
    margin-top: 10px;
  }

  .tech-dropdown-item {
    display: block;
    padding: 12px 16px;
    color: var(--tech-text-muted);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    margin-bottom: 4px;
  }

  .tech-dropdown-item:hover {
    background: rgba(66, 165, 245, 0.1);
    color: var(--tech-text);
    transform: translateX(5px);
  }

  /* 右侧操作区 */
  .tech-nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .tech-action-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 1px solid var(--tech-border);
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--tech-text-muted);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
  }

  .tech-action-btn:hover {
    border-color: var(--tech-primary);
    color: var(--tech-primary);
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.3);
  }

  /* CTA按钮 */
  .tech-cta-btn {
    padding: 12px 30px;
    background: var(--tech-gradient);
    border: none;
    border-radius: 60px;
    color: var(--tech-text);
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
  }

  .tech-cta-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .tech-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba({{ section.settings.primary_color | color_to_rgb }}, 0.4);
  }

  .tech-cta-btn:hover::before {
    width: 300px;
    height: 300px;
  }

  /* 移动端菜单按钮 */
  .tech-mobile-toggle {
    display: none;
    width: 45px;
    height: 45px;
    border: 1px solid var(--tech-border);
    border-radius: 8px;
    background: transparent;
    cursor: pointer;
    position: relative;
  }

  .tech-mobile-toggle span {
    display: block;
    width: 22px;
    height: 2px;
    background: var(--tech-text);
    margin: 5px auto;
    transition: all 0.3s ease;
  }

  /* 装饰元素 */
  .tech-nav-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
  }

  .tech-grid-bg {
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 100%;
    background-image:
      repeating-linear-gradient(90deg, transparent, transparent 50px, rgba(66, 165, 245, 0.03) 50px, rgba(66, 165, 245, 0.03) 51px),
      repeating-linear-gradient(0deg, transparent, transparent 50px, rgba(66, 165, 245, 0.03) 50px, rgba(66, 165, 245, 0.03) 51px);
    animation: gridMove 20s linear infinite;
  }

  /* 动画 */
  @keyframes navFadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes gridMove {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(50px);
    }
  }

  /* 搜索组件样式 */
  .tech-search-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .tech-search-container predictive-search.is-active {
    opacity: 1;
    visibility: visible;
  }

  .tech-search-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 600px;
    background: var(--tech-bg-darker);
    border: 1px solid var(--tech-border);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  }

  .tech-search-overlay .search__input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--tech-border);
    border-radius: 12px;
    color: var(--tech-text);
    font-size: 18px;
    padding: 15px 20px;
    width: 100%;
  }

  .tech-search-overlay .search__input::placeholder {
    color: var(--tech-text-muted);
  }

  .tech-search-overlay .search__input:focus {
    outline: none;
    border-color: var(--tech-primary);
    box-shadow: 0 0 0 3px rgba(66, 165, 245, 0.1);
  }

  .tech-search-overlay .btn--close-search {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: var(--tech-text-muted);
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .tech-search-overlay .btn--close-search:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--tech-text);
  }

  .tech-search-overlay .search__results {
    margin-top: 20px;
    max-height: 400px;
    overflow-y: auto;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .tech-navbar-inner {
      height: var(--tech-navbar-mobile-height);
    }

    .tech-nav-menu {
      position: fixed;
      top: 60px;
      left: 0;
      right: 0;
      background: rgba(26, 26, 46, 0.98);
      flex-direction: column;
      padding: 40px 20px;
      gap: 20px;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      overflow-y: auto; /* 允许滚动 */
      z-index: 9999; /* 确保在最顶层 */
      min-height: calc(100vh - 60px); /* 最小高度为屏幕高度减去导航栏高度 */
      max-height: none; /* 移除最大高度限制 */
      height: auto; /* 自动高度适应内容 */
    }

    .tech-nav-menu.active {
      transform: translateX(0);
    }

    .tech-mobile-toggle {
      display: block;
    }

    .tech-nav-actions {
      display: none;
    }

    .tech-dropdown {
      position: static;
      transform: none;
      margin: 10px 0;
      opacity: 1;
      visibility: visible;
      background: rgba(0, 0, 0, 0.5); /* 增强背景透明度 */
      border-radius: 8px; /* 添加圆角 */
      padding: 15px; /* 增加内边距 */
      width: 100%; /* 确保全宽 */
      box-sizing: border-box; /* 包含padding在宽度内 */
    }
  }
</style>

<nav class="tech-navbar" id="techNavbar">
  <div class="tech-nav-decoration">
    <div class="tech-grid-bg"></div>
  </div>

  <div class="tech-navbar-inner">
    <!-- Logo -->
    <a href="{{ routes.root_url }}" class="tech-logo">
      {% if section.settings.logo_image != blank %}
        {%- assign logo_alt = section.settings.logo_image.alt | default: shop.name | escape -%}
        {%- assign logo_width = section.settings.logo_width | default: 140 -%}
        <img
          src="{{ section.settings.logo_image | img_url: 'master' }}"
          alt="{{ logo_alt }}"
          class="tech-logo-image"
          style="max-width: {{ logo_width }}px;"
        >
      {% else %}
        <div class="tech-logo-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2"/>
            <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2"/>
            <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2"/>
          </svg>
        </div>
      {% endif %}
    </a>

    <!-- 主导航 -->
    <ul class="tech-nav-menu" id="techNavMenu">
      {% for link in linklists[section.settings.menu].links %}
        <li class="tech-nav-item">
          {% comment %}
          为特定菜单项添加自定义子菜单
          {% endcomment %}

          {% if link.title == '3D Printer' or link.title == '3D Printing' %}
            <!-- 3D Printer 菜单项 -->
            <span class="tech-nav-link has-dropdown">
              {{ link.title }}
              <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
              </svg>
            </span>
            <div class="tech-dropdown">
              {% if section.settings.printer_submenu_1_text != blank %}
                <a href="/pages/ir3-v2-show" class="tech-dropdown-item">
                  {{ section.settings.printer_submenu_1_text }}
                </a>
              {% endif %}
              {% if section.settings.printer_submenu_2_text != blank %}
                <a href="/none" class="tech-dropdown-item">
                  {{ section.settings.printer_submenu_2_text }}
                </a>
              {% endif %}
            </div>

          {% elsif link.title == 'Accessories' %}
            <!-- Accessories 菜单项 -->
            <span class="tech-nav-link has-dropdown">
              {{ link.title }}
              <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
              </svg>
            </span>
            <div class="tech-dropdown">
              {% if section.settings.accessories_submenu_1_text != blank %}
                <a href="/collections/ir3-v2-printer-accessories" class="tech-dropdown-item">
                  {{ section.settings.accessories_submenu_1_text }}
                </a>
              {% endif %}
              {% if section.settings.accessories_submenu_2_text != blank %}
                <a href="/none" class="tech-dropdown-item">
                  {{ section.settings.accessories_submenu_2_text }}
                </a>
              {% endif %}
              {% if section.settings.accessories_submenu_3_text != blank %}
                <a href="/collections/all" class="tech-dropdown-item">
                  {{ section.settings.accessories_submenu_3_text }}
                </a>
              {% endif %}
            </div>

          {% else %}
            <!-- 其他菜单项保持原有逻辑 -->
            <a href="{{ link.url }}" class="tech-nav-link">
              {{ link.title }}
              {% if link.links.size > 0 %}
                <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                  <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
                </svg>
              {% endif %}
            </a>

            {% if link.links.size > 0 %}
              <div class="tech-dropdown">
                {% for child_link in link.links %}
                  <a href="{{ child_link.url }}" class="tech-dropdown-item">
                    {{ child_link.title }}
                  </a>
                {% endfor %}
              </div>
            {% endif %}
          {% endif %}
        </li>
      {% endfor %}
    </ul>

    <!-- 右侧操作 -->
    <div class="tech-nav-actions">
      <!-- 搜索 -->
      <button class="tech-action-btn tech-search-btn" data-search-trigger>
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <circle cx="9" cy="9" r="6" stroke="currentColor" stroke-width="1.5"/>
          <path d="M13 13L17 17" stroke="currentColor" stroke-width="1.5"/>
        </svg>
      </button>

      <!-- 购物车 -->
      <a href="{{ routes.cart_url }}" class="tech-action-btn">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M3 3H17L15 11H5L3 3Z" stroke="currentColor" stroke-width="1.5"/>
          <circle cx="7" cy="17" r="1" fill="currentColor"/>
          <circle cx="13" cy="17" r="1" fill="currentColor"/>
        </svg>
        {% if cart.item_count > 0 %}
          <span style="position: absolute; top: -5px; right: -5px; background: var(--tech-gradient); color: white; font-size: 11px; padding: 2px 6px; border-radius: 10px;">
            {{ cart.item_count }}
          </span>
        {% endif %}
      </a>

      <!-- CTA按钮 -->
      {% if section.settings.show_cta %}
        <a href="{{ section.settings.cta_link }}" class="tech-cta-btn">
          {{ section.settings.cta_text }}
        </a>
      {% endif %}
    </div>

    <!-- 移动端菜单按钮 -->
    <button class="tech-mobile-toggle" onclick="toggleMobileMenu()">
      <span></span>
      <span></span>
      <span></span>
    </button>
  </div>

  <!-- 搜索组件 -->
  <div class="tech-search-container">
    <div class="tech-search-overlay">
      {% render 'predictive-search', context: 'header' %}
    </div>
  </div>
</nav>

<script>
  // 滚动效果
  window.addEventListener('scroll', function() {
    const navbar = document.getElementById('techNavbar');
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // 初始化导航栏，处理内容偏移
  document.addEventListener('DOMContentLoaded', function() {
    // 等待DOM完全加载
    setTimeout(function() {
      const navbar = document.getElementById('techNavbar');
      if (navbar) {
        // 获取导航栏高度
        const navbarHeight = navbar.offsetHeight;
        
        // 添加初始化标记
        document.body.classList.add('tech-navbar-initialized');
        
        // 处理特殊组件的顶部边距，以确保视觉内容不被导航栏遮挡
        const fullScreenSections = document.querySelectorAll('.custom-video-banner--100vh, [style*="height: 100vh"]');
        fullScreenSections.forEach(function(section) {
          // 对于全屏幕高度的组件，增加顶部内边距
          if (window.innerWidth > 768) {
            section.style.paddingTop = navbarHeight + 'px';
          } else {
            const mobileNavHeight = navbar.querySelector('.tech-navbar-inner').offsetHeight;
            section.style.paddingTop = mobileNavHeight + 'px';
          }
        });
      }
    }, 100);
  });

  // 移动端菜单切换
  function toggleMobileMenu() {
    const menu = document.getElementById('techNavMenu');
    menu.classList.toggle('active');
  }

  // 搜索功能
  const searchBtn = document.querySelector('.tech-search-btn');
  const searchContainer = document.querySelector('.tech-search-container');

  if (searchBtn && searchContainer) {
    searchBtn.addEventListener('click', function(evt) {
      evt.preventDefault();
      evt.stopPropagation();

      // 显示搜索容器
      searchContainer.style.opacity = '1';
      searchContainer.style.visibility = 'visible';

      // 触发搜索打开事件
      document.dispatchEvent(new CustomEvent('predictive-search:open', {
        detail: {
          context: 'header'
        },
        bubbles: true
      }));
    });

    // 点击背景关闭搜索
    searchContainer.addEventListener('click', function(evt) {
      if (evt.target === searchContainer) {
        searchContainer.style.opacity = '0';
        searchContainer.style.visibility = 'hidden';
        document.dispatchEvent(new CustomEvent('predictive-search:close'));
      }
    });

    // 监听搜索关闭事件
    document.addEventListener('predictive-search:close-all', function() {
      searchContainer.style.opacity = '0';
      searchContainer.style.visibility = 'hidden';
    });
  }

  // 磁性按钮效果
  document.querySelectorAll('.tech-action-btn, .tech-cta-btn').forEach(btn => {
    btn.addEventListener('mousemove', function(e) {
      const rect = btn.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;

      btn.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) scale(1.05)`;
    });

    btn.addEventListener('mouseleave', function() {
      btn.style.transform = '';
    });
  });
</script>

{% schema %}
{
  "name": "科技风格导航栏",
  "settings": [
    {
      "type": "header",
      "content": "Logo设置"
    },
    {
      "type": "image_picker",
      "id": "logo_image",
      "label": "Logo图片"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 300,
      "step": 10,
      "default": 140,
      "unit": "px",
      "label": "Logo宽度"
    },
    {
      "type": "header",
      "content": "导航菜单"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "主菜单",
      "default": "main-menu"
    },
    {
      "type": "header",
      "content": "3D Printer 子菜单"
    },
    {
      "type": "text",
      "id": "printer_submenu_1_text",
      "label": "子菜单项 1 文字",
      "default": "IR3 V2"
    },
    {
      "type": "url",
      "id": "printer_submenu_1_url",
      "label": "子菜单项 1 链接"
    },
    {
      "type": "text",
      "id": "printer_submenu_2_text",
      "label": "子菜单项 2 文字",
      "default": "IR3 V1"
    },
    {
      "type": "url",
      "id": "printer_submenu_2_url",
      "label": "子菜单项 2 链接"
    },
    {
      "type": "header",
      "content": "Accessories 子菜单"
    },
    {
      "type": "text",
      "id": "accessories_submenu_1_text",
      "label": "子菜单项 1 文字",
      "default": "IR3 V2 Accessories"
    },
    {
      "type": "url",
      "id": "accessories_submenu_1_url",
      "label": "子菜单项 1 链接"
    },
    {
      "type": "text",
      "id": "accessories_submenu_2_text",
      "label": "子菜单项 2 文字",
      "default": "IR3 V1 Accessories"
    },
    {
      "type": "url",
      "id": "accessories_submenu_2_url",
      "label": "子菜单项 2 链接"
    },
    {
      "type": "text",
      "id": "accessories_submenu_3_text",
      "label": "子菜单项 3 文字",
      "default": "Other Accessories"
    },
    {
      "type": "url",
      "id": "accessories_submenu_3_url",
      "label": "子菜单项 3 链接"
    },
    {
      "type": "header",
      "content": "CTA按钮"
    },
    {
      "type": "checkbox",
      "id": "show_cta",
      "label": "显示CTA按钮",
      "default": true
    },
    {
      "type": "text",
      "id": "cta_text",
      "label": "CTA文字",
      "default": "立即体验"
    },
    {
      "type": "url",
      "id": "cta_link",
      "label": "CTA链接"
    },
    {
      "type": "header",
      "content": "样式设置"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "主色调",
      "default": "#42a5f5"
    },
    {
      "type": "color",
      "id": "secondary_color",
      "label": "辅助色",
      "default": "#1de9b6"
    },
    {
      "type": "checkbox",
      "id": "enable_animations",
      "label": "启用动画效果",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "科技风格导航栏",
      "category": "导航"
    }
  ]
}
{% endschema %}
